<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>加密货币价格更新测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .status {
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 600;
            text-align: center;
        }
        
        .status.loading {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .crypto-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .crypto-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .crypto-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }
        
        .crypto-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .crypto-symbol {
            font-size: 24px;
            font-weight: 700;
            color: #1a1a1a;
            margin-right: 10px;
        }
        
        .crypto-name {
            font-size: 14px;
            color: #666;
        }
        
        .crypto-price {
            font-size: 28px;
            font-weight: 700;
            color: #1a1a1a;
            margin-bottom: 8px;
        }
        
        .crypto-change {
            font-size: 16px;
            font-weight: 600;
            padding: 4px 8px;
            border-radius: 6px;
        }
        
        .crypto-change.positive {
            color: #00d4aa;
            background: rgba(0, 212, 170, 0.1);
        }
        
        .crypto-change.negative {
            color: #ff6b6b;
            background: rgba(255, 107, 107, 0.1);
        }
        
        .crypto-change.neutral {
            color: #666;
            background: #f8f9fa;
        }
        
        .last-updated {
            font-size: 12px;
            color: #999;
            margin-top: 10px;
        }
        
        .update-animation {
            animation: priceUpdate 1s ease-out;
        }
        
        @keyframes priceUpdate {
            0% { background-color: rgba(0, 212, 170, 0.3); }
            100% { background-color: transparent; }
        }
        
        .controls {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .btn {
            background: #0052ff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin: 0 10px;
            transition: background 0.2s ease;
        }
        
        .btn:hover {
            background: #0041cc;
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 加密货币实时价格测试</h1>
        <p>测试CoinGecko API的实时价格更新功能</p>
    </div>
    
    <div id="status" class="status loading">
        正在加载价格数据...
    </div>
    
    <div class="controls">
        <button id="refreshBtn" class="btn">手动刷新</button>
        <button id="toggleBtn" class="btn">暂停自动更新</button>
    </div>
    
    <div id="cryptoGrid" class="crypto-grid">
        <!-- 价格卡片将在这里动态生成 -->
    </div>

    <script>
        class CryptoPriceTest {
            constructor() {
                this.updateInterval = 30000; // 30秒更新一次
                this.priceData = {};
                this.lastUpdateTime = 0;
                this.isAutoUpdateEnabled = true;
                this.updateTimer = null;
                
                // 支持的加密货币
                this.cryptos = [
                    { id: 'bitcoin', symbol: 'BTC', name: 'Bitcoin' },
                    { id: 'ethereum', symbol: 'ETH', name: 'Ethereum' },
                    { id: 'binancecoin', symbol: 'BNB', name: 'BNB' },
                    { id: 'cardano', symbol: 'ADA', name: 'Cardano' },
                    { id: 'solana', symbol: 'SOL', name: 'Solana' },
                    { id: 'polkadot', symbol: 'DOT', name: 'Polkadot' },
                    { id: 'dogecoin', symbol: 'DOGE', name: 'Dogecoin' },
                    { id: 'avalanche-2', symbol: 'AVAX', name: 'Avalanche' },
                    { id: 'polygon', symbol: 'MATIC', name: 'Polygon' },
                    { id: 'chainlink', symbol: 'LINK', name: 'Chainlink' },
                    { id: 'litecoin', symbol: 'LTC', name: 'Litecoin' },
                    { id: 'bitcoin-cash', symbol: 'BCH', name: 'Bitcoin Cash' }
                ];
                
                this.init();
            }
            
            async init() {
                console.log('🚀 初始化价格测试系统...');
                
                this.setupEventListeners();
                this.createCryptoCards();
                
                // 立即获取一次价格
                await this.fetchPrices();
                
                // 启动自动更新
                this.startAutoUpdate();
            }
            
            setupEventListeners() {
                document.getElementById('refreshBtn').addEventListener('click', () => {
                    this.fetchPrices();
                });
                
                document.getElementById('toggleBtn').addEventListener('click', () => {
                    this.toggleAutoUpdate();
                });
            }
            
            createCryptoCards() {
                const grid = document.getElementById('cryptoGrid');
                grid.innerHTML = '';
                
                this.cryptos.forEach(crypto => {
                    const card = document.createElement('div');
                    card.className = 'crypto-card';
                    card.id = `card-${crypto.symbol}`;
                    
                    card.innerHTML = `
                        <div class="crypto-header">
                            <div class="crypto-symbol">${crypto.symbol}</div>
                            <div class="crypto-name">${crypto.name}</div>
                        </div>
                        <div class="crypto-price" id="price-${crypto.symbol}">$0.00</div>
                        <div class="crypto-change neutral" id="change-${crypto.symbol}">+0.00%</div>
                        <div class="last-updated" id="updated-${crypto.symbol}">从未更新</div>
                    `;
                    
                    grid.appendChild(card);
                });
            }
            
            async fetchPrices() {
                try {
                    const now = Date.now();
                    
                    // 防止频繁请求
                    if (now - this.lastUpdateTime < 25000) {
                        console.log('⏰ 请求过于频繁，跳过此次更新');
                        return false;
                    }
                    
                    this.updateStatus('loading', '正在获取最新价格...');
                    console.log('🔄 获取最新价格数据...');
                    
                    const coinIds = this.cryptos.map(c => c.id).join(',');
                    const url = `https://api.coingecko.com/api/v3/simple/price?ids=${coinIds}&vs_currencies=usd&include_24hr_change=true&include_last_updated_at=true`;
                    
                    const response = await fetch(url);
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    
                    const data = await response.json();
                    
                    // 更新价格数据
                    this.priceData = data;
                    this.lastUpdateTime = now;
                    
                    // 更新显示
                    this.updateDisplay();
                    
                    this.updateStatus('success', `✅ 成功更新 ${Object.keys(data).length} 个币种价格 - ${new Date().toLocaleTimeString()}`);
                    console.log(`✅ 成功更新价格数据:`, Object.keys(data));
                    
                    return true;
                    
                } catch (error) {
                    console.error('❌ 获取价格数据失败:', error);
                    this.updateStatus('error', `❌ 获取价格失败: ${error.message}`);
                    return false;
                }
            }
            
            updateDisplay() {
                this.cryptos.forEach(crypto => {
                    const data = this.priceData[crypto.id];
                    if (!data) return;
                    
                    const priceElement = document.getElementById(`price-${crypto.symbol}`);
                    const changeElement = document.getElementById(`change-${crypto.symbol}`);
                    const updatedElement = document.getElementById(`updated-${crypto.symbol}`);
                    const cardElement = document.getElementById(`card-${crypto.symbol}`);
                    
                    // 更新价格
                    const price = data.usd || 0;
                    priceElement.textContent = this.formatPrice(price);
                    
                    // 更新涨跌幅
                    const change = data.usd_24h_change || 0;
                    changeElement.textContent = this.formatChange(change);
                    changeElement.className = `crypto-change ${this.getChangeClass(change)}`;
                    
                    // 更新时间
                    const lastUpdated = data.last_updated_at ? new Date(data.last_updated_at * 1000) : new Date();
                    updatedElement.textContent = `更新于 ${lastUpdated.toLocaleTimeString()}`;
                    
                    // 添加更新动画
                    cardElement.classList.add('update-animation');
                    setTimeout(() => {
                        cardElement.classList.remove('update-animation');
                    }, 1000);
                });
            }
            
            formatPrice(price) {
                if (price >= 1) {
                    return `$${price.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}`;
                } else if (price >= 0.01) {
                    return `$${price.toFixed(4)}`;
                } else {
                    return `$${price.toFixed(6)}`;
                }
            }
            
            formatChange(change) {
                const sign = change >= 0 ? '+' : '';
                return `${sign}${change.toFixed(2)}%`;
            }
            
            getChangeClass(change) {
                if (change > 0) return 'positive';
                if (change < 0) return 'negative';
                return 'neutral';
            }
            
            updateStatus(type, message) {
                const statusElement = document.getElementById('status');
                statusElement.className = `status ${type}`;
                statusElement.textContent = message;
            }
            
            startAutoUpdate() {
                if (this.updateTimer) {
                    clearInterval(this.updateTimer);
                }
                
                this.updateTimer = setInterval(() => {
                    if (this.isAutoUpdateEnabled) {
                        this.fetchPrices();
                    }
                }, this.updateInterval);
                
                console.log('⏰ 自动更新已启动，每30秒更新一次');
            }
            
            toggleAutoUpdate() {
                const toggleBtn = document.getElementById('toggleBtn');
                
                if (this.isAutoUpdateEnabled) {
                    this.isAutoUpdateEnabled = false;
                    toggleBtn.textContent = '启动自动更新';
                    this.updateStatus('loading', '⏸️ 自动更新已暂停');
                    console.log('⏸️ 自动更新已暂停');
                } else {
                    this.isAutoUpdateEnabled = true;
                    toggleBtn.textContent = '暂停自动更新';
                    this.updateStatus('success', '▶️ 自动更新已启动');
                    console.log('▶️ 自动更新已启动');
                }
            }
        }
        
        // 启动测试系统
        document.addEventListener('DOMContentLoaded', () => {
            window.cryptoPriceTest = new CryptoPriceTest();
        });
    </script>
</body>
</html>
