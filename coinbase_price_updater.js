/**
 * Coinbase实时价格更新器
 * 连接到本地WebSocket服务获取官方Coinbase价格数据
 */

class CoinbasePriceUpdater {
    constructor() {
        this.ws = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000; // 1秒
        this.isConnected = false;
        
        // 支持的币种映射
        this.symbolMap = {
            'BTC-USD': 'BTC',
            'ETH-USD': 'ETH', 
            'ADA-USD': 'ADA',
            'SOL-USD': 'SOL',
            'XRP-USD': 'XRP',
            'DOGE-USD': 'DOGE',
            'MATIC-USD': 'MATIC',
            'LTC-USD': 'LTC',
            'LINK-USD': 'LINK',
            'DOT-USD': 'DOT',
            'UNI-USD': 'UNI',
            'AVAX-USD': 'AVAX',
            'ATOM-USD': 'ATOM',
            'ALGO-USD': 'ALGO',
            'VET-USD': 'VET'
        };
        
        // 价格数据缓存
        this.priceData = {};
        
        // 初始化
        this.init();
    }
    
    init() {
        console.log('初始化Coinbase价格更新器...');
        this.connect();
        
        // 页面卸载时关闭连接
        window.addEventListener('beforeunload', () => {
            this.disconnect();
        });
    }
    
    connect() {
        try {
            console.log('连接到Coinbase价格服务...');
            this.ws = new WebSocket('ws://localhost:8765');
            
            this.ws.onopen = (event) => {
                console.log('WebSocket连接已建立');
                this.isConnected = true;
                this.reconnectAttempts = 0;
                
                // 订阅所有支持的币种
                this.subscribe(Object.keys(this.symbolMap));
                
                // 显示连接状态
                this.updateConnectionStatus(true);
            };
            
            this.ws.onmessage = (event) => {
                try {
                    const message = JSON.parse(event.data);
                    this.handleMessage(message);
                } catch (error) {
                    console.error('解析WebSocket消息失败:', error);
                }
            };
            
            this.ws.onclose = (event) => {
                console.log('WebSocket连接已关闭');
                this.isConnected = false;
                this.updateConnectionStatus(false);
                
                // 尝试重连
                if (this.reconnectAttempts < this.maxReconnectAttempts) {
                    this.reconnectAttempts++;
                    console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
                    
                    setTimeout(() => {
                        this.connect();
                    }, this.reconnectDelay * this.reconnectAttempts);
                } else {
                    console.error('重连失败，已达到最大重试次数');
                    this.showError('无法连接到价格服务，请刷新页面重试');
                }
            };
            
            this.ws.onerror = (error) => {
                console.error('WebSocket错误:', error);
                this.showError('价格服务连接错误');
            };
            
        } catch (error) {
            console.error('创建WebSocket连接失败:', error);
            this.showError('无法连接到价格服务');
        }
    }
    
    disconnect() {
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
    }
    
    subscribe(symbols) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            const message = {
                type: 'subscribe',
                symbols: symbols
            };
            this.ws.send(JSON.stringify(message));
            console.log('订阅币种:', symbols);
        }
    }
    
    handleMessage(message) {
        switch (message.type) {
            case 'initial_data':
                console.log('收到初始价格数据');
                this.priceData = message.data;
                this.updateAllPrices();
                break;
                
            case 'subscribed_data':
                console.log('收到订阅数据');
                Object.assign(this.priceData, message.data);
                this.updateAllPrices();
                break;
                
            case 'price_update':
                console.log('收到价格更新');
                Object.assign(this.priceData, message.data);
                this.updatePrices(message.data);
                break;
                
            default:
                console.log('未知消息类型:', message.type);
        }
    }
    
    updateAllPrices() {
        Object.keys(this.priceData).forEach(symbol => {
            this.updateCoinPrice(symbol, this.priceData[symbol]);
        });
    }
    
    updatePrices(data) {
        Object.keys(data).forEach(symbol => {
            this.updateCoinPrice(symbol, data[symbol]);
        });
    }
    
    updateCoinPrice(symbol, data) {
        const shortSymbol = this.symbolMap[symbol];
        if (!shortSymbol) return;
        
        // 查找页面中的价格元素
        const priceElements = this.findPriceElements(shortSymbol);
        
        priceElements.forEach(element => {
            this.updateElement(element, data);
        });
        
        // 添加价格变化动画
        this.addPriceChangeAnimation(priceElements, data.change_percent_24h);
    }
    
    findPriceElements(symbol) {
        const elements = [];
        
        // 通用选择器
        const selectors = [
            `[data-symbol="${symbol}"]`,
            `[data-coin="${symbol}"]`,
            `[data-currency="${symbol}"]`,
            `.${symbol.toLowerCase()}-price`,
            `.price-${symbol.toLowerCase()}`,
            `#${symbol.toLowerCase()}-price`,
            `#price-${symbol.toLowerCase()}`
        ];
        
        selectors.forEach(selector => {
            const found = document.querySelectorAll(selector);
            elements.push(...found);
        });
        
        // 文本内容匹配
        const textElements = document.querySelectorAll('*');
        textElements.forEach(el => {
            if (el.textContent && el.textContent.includes(symbol)) {
                elements.push(el);
            }
        });
        
        return [...new Set(elements)]; // 去重
    }
    
    updateElement(element, data) {
        const price = data.price;
        const change = data.change_percent_24h;
        
        // 更新价格
        if (element.classList.contains('price') || element.dataset.type === 'price') {
            element.textContent = this.formatPrice(price);
        }
        
        // 更新变化百分比
        if (element.classList.contains('change') || element.dataset.type === 'change') {
            element.textContent = this.formatChange(change);
            this.updateChangeColor(element, change);
        }
        
        // 更新完整信息
        if (element.dataset.symbol || element.dataset.coin) {
            const priceSpan = element.querySelector('.price, [data-type="price"]');
            const changeSpan = element.querySelector('.change, [data-type="change"]');
            
            if (priceSpan) {
                priceSpan.textContent = this.formatPrice(price);
            }
            
            if (changeSpan) {
                changeSpan.textContent = this.formatChange(change);
                this.updateChangeColor(changeSpan, change);
            }
        }
    }
    
    formatPrice(price) {
        if (price >= 1) {
            return `$${price.toFixed(2)}`;
        } else if (price >= 0.01) {
            return `$${price.toFixed(4)}`;
        } else {
            return `$${price.toFixed(6)}`;
        }
    }
    
    formatChange(change) {
        const sign = change >= 0 ? '+' : '';
        return `${sign}${change.toFixed(2)}%`;
    }
    
    updateChangeColor(element, change) {
        element.classList.remove('positive', 'negative', 'neutral');
        
        if (change > 0) {
            element.classList.add('positive');
            element.style.color = '#00d4aa';
        } else if (change < 0) {
            element.classList.add('negative');
            element.style.color = '#ff6b6b';
        } else {
            element.classList.add('neutral');
            element.style.color = '#666';
        }
    }
    
    addPriceChangeAnimation(elements, change) {
        elements.forEach(element => {
            element.classList.remove('price-up', 'price-down');
            
            if (change > 0) {
                element.classList.add('price-up');
            } else if (change < 0) {
                element.classList.add('price-down');
            }
            
            // 移除动画类
            setTimeout(() => {
                element.classList.remove('price-up', 'price-down');
            }, 1000);
        });
    }
    
    updateConnectionStatus(connected) {
        // 查找状态指示器
        const statusElements = document.querySelectorAll('.connection-status, #connection-status');
        
        statusElements.forEach(element => {
            if (connected) {
                element.textContent = '已连接';
                element.className = 'connection-status connected';
                element.style.color = '#00d4aa';
            } else {
                element.textContent = '连接中...';
                element.className = 'connection-status disconnected';
                element.style.color = '#ff6b6b';
            }
        });
        
        // 如果没有状态元素，创建一个
        if (statusElements.length === 0 && document.body) {
            this.createStatusIndicator(connected);
        }
    }
    
    createStatusIndicator(connected) {
        const indicator = document.createElement('div');
        indicator.id = 'connection-status';
        indicator.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 10000;
            background: rgba(0,0,0,0.8);
            color: white;
        `;
        
        if (connected) {
            indicator.textContent = '价格数据已连接';
            indicator.style.borderLeft = '3px solid #00d4aa';
        } else {
            indicator.textContent = '价格数据连接中...';
            indicator.style.borderLeft = '3px solid #ff6b6b';
        }
        
        document.body.appendChild(indicator);
        
        // 3秒后自动隐藏
        setTimeout(() => {
            if (indicator.parentNode) {
                indicator.parentNode.removeChild(indicator);
            }
        }, 3000);
    }
    
    showError(message) {
        console.error(message);
        
        // 显示错误提示
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 50px;
            right: 10px;
            padding: 10px;
            background: #ff6b6b;
            color: white;
            border-radius: 4px;
            z-index: 10001;
            max-width: 300px;
        `;
        errorDiv.textContent = message;
        
        document.body.appendChild(errorDiv);
        
        // 5秒后自动移除
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.parentNode.removeChild(errorDiv);
            }
        }, 5000);
    }
    
    // 公共API
    getPrice(symbol) {
        const fullSymbol = `${symbol}-USD`;
        return this.priceData[fullSymbol] || null;
    }
    
    getAllPrices() {
        return this.priceData;
    }
    
    isConnectedToService() {
        return this.isConnected;
    }
}

// CSS样式
const style = document.createElement('style');
style.textContent = `
    .price-up {
        animation: priceUp 1s ease-out;
    }
    
    .price-down {
        animation: priceDown 1s ease-out;
    }
    
    @keyframes priceUp {
        0% { background-color: rgba(0, 212, 170, 0.3); }
        100% { background-color: transparent; }
    }
    
    @keyframes priceDown {
        0% { background-color: rgba(255, 107, 107, 0.3); }
        100% { background-color: transparent; }
    }
    
    .positive {
        color: #00d4aa !important;
    }
    
    .negative {
        color: #ff6b6b !important;
    }
    
    .neutral {
        color: #666 !important;
    }
`;
document.head.appendChild(style);

// 自动初始化
let coinbasePriceUpdater;

if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        coinbasePriceUpdater = new CoinbasePriceUpdater();
    });
} else {
    coinbasePriceUpdater = new CoinbasePriceUpdater();
}

// 导出到全局作用域
window.CoinbasePriceUpdater = CoinbasePriceUpdater;
window.coinbasePriceUpdater = coinbasePriceUpdater;
