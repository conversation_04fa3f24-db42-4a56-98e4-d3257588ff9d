/**
 * CSP (Content Security Policy) 配置管理
 * 用于动态设置和管理内容安全策略
 */

class CSPManager {
  constructor() {
    this.config = {
      // 基础策略
      'default-src': ["'self'", "data:", "blob:"],
      
      // 脚本源 - 允许内联脚本和eval（用于动态加载）
      'script-src': [
        "'self'", 
        "'unsafe-inline'", 
        "'unsafe-eval'", 
        "data:", 
        "blob:",
        "https://api.coingecko.com",
        "https://api.coinlore.net", 
        "https://api.coincap.io",
        "https://www.coinbase.com",
        "https://*.coinbase.com"
      ],
      
      // 样式源 - 允许内联样式
      'style-src': [
        "'self'", 
        "'unsafe-inline'", 
        "data:", 
        "blob:"
      ],
      
      // 图片源 - 允许所有HTTPS和HTTP图片
      'img-src': [
        "'self'", 
        "data:", 
        "blob:", 
        "https:", 
        "http:"
      ],
      
      // 字体源
      'font-src': [
        "'self'", 
        "data:", 
        "blob:"
      ],
      
      // 连接源 - API和WebSocket连接
      'connect-src': [
        "'self'",
        "https://api.coingecko.com",
        "https://api.coinlore.net",
        "https://api.coincap.io", 
        "https://www.coinbase.com",
        "https://*.coinbase.com",
        "wss:",
        "ws:"
      ],
      
      // 框架源
      'frame-src': [
        "'self'",
        "https://www.coinbase.com",
        "https://*.coinbase.com"
      ],
      
      // 禁止对象嵌入
      'object-src': ["'none'"],
      
      // 基础URI限制
      'base-uri': ["'self'"],
      
      // 表单提交限制
      'form-action': [
        "'self'",
        "https://www.coinbase.com",
        "https://*.coinbase.com"
      ],
      
      // 框架祖先限制
      'frame-ancestors': ["'self'"],
      
      // 升级不安全请求
      'upgrade-insecure-requests': []
    };
  }

  /**
   * 生成CSP字符串
   * @returns {string} CSP策略字符串
   */
  generateCSP() {
    const policies = [];
    
    for (const [directive, sources] of Object.entries(this.config)) {
      if (sources.length === 0) {
        policies.push(directive);
      } else {
        policies.push(`${directive} ${sources.join(' ')}`);
      }
    }
    
    return policies.join('; ');
  }

  /**
   * 添加源到指定指令
   * @param {string} directive - CSP指令
   * @param {string} source - 要添加的源
   */
  addSource(directive, source) {
    if (this.config[directive] && !this.config[directive].includes(source)) {
      this.config[directive].push(source);
    }
  }

  /**
   * 移除源从指定指令
   * @param {string} directive - CSP指令
   * @param {string} source - 要移除的源
   */
  removeSource(directive, source) {
    if (this.config[directive]) {
      const index = this.config[directive].indexOf(source);
      if (index > -1) {
        this.config[directive].splice(index, 1);
      }
    }
  }

  /**
   * 设置CSP到页面
   */
  applyCSP() {
    const cspString = this.generateCSP();
    
    // 创建或更新meta标签
    let cspMeta = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
    if (!cspMeta) {
      cspMeta = document.createElement('meta');
      cspMeta.setAttribute('http-equiv', 'Content-Security-Policy');
      document.head.appendChild(cspMeta);
    }
    cspMeta.setAttribute('content', cspString);
    
    console.log('CSP Applied:', cspString);
  }

  /**
   * 获取严格模式CSP配置
   * @returns {Object} 严格模式配置
   */
  getStrictConfig() {
    return {
      'default-src': ["'self'"],
      'script-src': ["'self'"],
      'style-src': ["'self'", "'unsafe-inline'"], // 保留内联样式支持
      'img-src': ["'self'", "data:", "https:"],
      'font-src': ["'self'", "data:"],
      'connect-src': [
        "'self'",
        "https://api.coingecko.com",
        "https://api.coinlore.net", 
        "https://api.coincap.io"
      ],
      'frame-src': ["'none'"],
      'object-src': ["'none'"],
      'base-uri': ["'self'"],
      'form-action': ["'self'"],
      'frame-ancestors': ["'none'"],
      'upgrade-insecure-requests': []
    };
  }

  /**
   * 应用严格模式CSP
   */
  applyStrictCSP() {
    this.config = this.getStrictConfig();
    this.applyCSP();
  }

  /**
   * 检查CSP违规
   */
  setupViolationReporting() {
    document.addEventListener('securitypolicyviolation', (e) => {
      console.warn('CSP Violation:', {
        directive: e.violatedDirective,
        blockedURI: e.blockedURI,
        lineNumber: e.lineNumber,
        columnNumber: e.columnNumber,
        sourceFile: e.sourceFile
      });
    });
  }
}

// 导出CSP管理器
if (typeof module !== 'undefined' && module.exports) {
  module.exports = CSPManager;
} else if (typeof window !== 'undefined') {
  window.CSPManager = CSPManager;
}

// 自动初始化
if (typeof window !== 'undefined') {
  window.cspManager = new CSPManager();
  
  // 设置违规报告
  window.cspManager.setupViolationReporting();
  
  // 在页面加载完成后应用CSP
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      window.cspManager.applyCSP();
    });
  } else {
    window.cspManager.applyCSP();
  }
}
