// 1. 确保 DOM 加载完成后再绑定事件
document.addEventListener('DOMContentLoaded', function() {

  // 2. "Subscribe Now" 按钮：弹窗 + 跳转
  document.getElementById('subscribeNowBtn').addEventListener('click', function() {
    // 先显示弹窗
    alert('Thank you for your interest! We will notify you when this feature is available in your country.');
    
    // 然后跳转到 shouji.html
    window.location.href = 'shouji.html'; // 确保路径正确
  });

  // 3. "返回" 按钮：关闭弹窗（如果有）
  document.getElementById('goBackBtn').addEventListener('click', function() {
    // 如果有弹窗关闭函数，调用它
    if (typeof hideBuysNotSupportedModal === 'function') {
      hideBuysNotSupportedModal();
    }
  });
});