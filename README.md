# Coinbase实时价格更新系统

这个项目为 `explore.html` 页面提供实时的Coinbase官方价格数据，包括价格和24小时涨跌幅信息。

## 功能特性

- ✅ 实时价格更新
- ✅ 24小时涨跌幅显示
- ✅ 支持15种主流加密货币
- ✅ WebSocket连接，低延迟更新
- ✅ 自动重连机制
- ✅ 移动端友好界面
- ✅ 无需API密钥（使用公共端点）

## 支持的加密货币

- BTC (Bitcoin)
- ETH (Ethereum)
- ADA (Cardano)
- SOL (Solana)
- XRP (Ripple)
- DOGE (Dogecoin)
- MATIC (Polygon)
- LTC (Litecoin)
- LINK (Chainlink)
- DOT (Polkadot)
- UNI (Uniswap)
- AVAX (Avalanche)
- ATOM (Cosmos)
- ALGO (Algorand)
- VET (VeChain)

## 安装和运行

### 方法1: 使用简化版服务（推荐）

1. **安装Python依赖**
```bash
pip install aiohttp websockets
```

2. **启动价格服务**
```bash
python simple_price_service.py
```

3. **打开网页**
在浏览器中打开 `explore.html` 文件

### 方法2: 使用完整版Coinbase SDK

1. **安装Coinbase SDK**
```bash
pip install coinbase-advanced-py websockets
```

2. **启动完整版服务**
```bash
python coinbase_price_service.py
```

3. **打开网页**
在浏览器中打开 `explore.html` 文件

## 文件说明

### 核心文件

- `explore.html` - 主页面，已集成价格更新功能
- `coinbase_price_updater.js` - 前端价格更新脚本
- `simple_price_service.py` - 简化版价格服务（推荐）
- `coinbase_price_service.py` - 完整版价格服务

### 配置文件

- `README.md` - 本说明文档
- `requirements.txt` - Python依赖列表

## 使用说明

### 启动服务

1. 在终端中运行价格服务：
```bash
python simple_price_service.py
```

2. 看到以下输出表示服务启动成功：
```
启动价格服务器 ws://localhost:8765
服务器已启动，等待客户端连接...
```

### 打开网页

1. 在浏览器中打开 `explore.html`
2. 页面左下角会显示实时价格面板
3. 右上角会显示连接状态

### 价格更新

- 价格每30秒自动更新一次
- 价格上涨时显示绿色，下跌时显示红色
- 支持价格变化动画效果

## 技术架构

### 后端服务

- **WebSocket服务器**: 提供实时数据推送
- **Coinbase API**: 获取官方价格数据
- **异步处理**: 支持多客户端并发连接

### 前端集成

- **WebSocket客户端**: 接收实时价格数据
- **自动重连**: 网络断开时自动重连
- **DOM更新**: 智能识别页面中的价格元素
- **动画效果**: 价格变化时的视觉反馈

## API端点

### WebSocket消息格式

**客户端订阅消息**:
```json
{
  "type": "subscribe",
  "symbols": ["BTC-USD", "ETH-USD"]
}
```

**服务器价格更新**:
```json
{
  "type": "price_update",
  "data": {
    "BTC-USD": {
      "symbol": "BTC-USD",
      "price": 45000.00,
      "change_24h": 1200.00,
      "change_percent_24h": 2.74,
      "volume_24h": 1500000000,
      "last_updated": "2024-01-01T12:00:00"
    }
  }
}
```

## 自定义配置

### 修改支持的币种

在 `simple_price_service.py` 中修改 `supported_pairs` 列表：

```python
self.supported_pairs = [
    'BTC-USD', 'ETH-USD', 'ADA-USD',
    # 添加更多币种...
]
```

### 修改更新频率

在 `simple_price_service.py` 中修改更新间隔：

```python
# 等待30秒后再次更新
await asyncio.sleep(30)  # 改为你想要的秒数
```

### 自定义价格显示

在 `coinbase_price_updater.js` 中修改 `formatPrice` 方法：

```javascript
formatPrice(price) {
    // 自定义价格格式化逻辑
    return `$${price.toFixed(2)}`;
}
```

## 故障排除

### 常见问题

1. **连接失败**
   - 确保价格服务正在运行
   - 检查端口8765是否被占用
   - 确认防火墙设置

2. **价格不更新**
   - 检查网络连接
   - 查看浏览器控制台错误信息
   - 重启价格服务

3. **页面显示异常**
   - 确保 `coinbase_price_updater.js` 文件路径正确
   - 检查浏览器是否支持WebSocket
   - 清除浏览器缓存

### 调试模式

启用详细日志：

```bash
# 设置日志级别为DEBUG
export PYTHONPATH=.
python -c "
import logging
logging.basicConfig(level=logging.DEBUG)
exec(open('simple_price_service.py').read())
"
```

## 性能优化

- 服务支持多客户端并发连接
- 使用异步I/O提高性能
- 智能的DOM元素查找和更新
- 最小化网络请求频率

## 安全考虑

- 使用公共API端点，无需API密钥
- WebSocket连接仅限本地访问
- 不存储敏感用户数据
- 遵循Coinbase API使用条款

## 许可证

本项目仅供学习和演示使用。请遵守Coinbase API的使用条款。

## 支持

如有问题或建议，请查看：
- [Coinbase Advanced API文档](https://docs.cdp.coinbase.com/advanced-trade/docs/welcome/)
- [WebSocket API文档](https://docs.cdp.coinbase.com/advanced-trade/docs/ws-overview)
