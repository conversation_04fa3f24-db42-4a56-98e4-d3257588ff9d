<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Real-time Crypto Prices - Coinbase Style</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
        }
        
        .header {
            background: white;
            padding: 16px 20px;
            border-bottom: 1px solid #e5e7eb;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header h1 {
            font-size: 24px;
            font-weight: 700;
            color: #0052ff;
        }
        
        .status {
            padding: 12px 20px;
            background: #dcfce7;
            border-left: 4px solid #10b981;
            margin: 16px 20px;
            border-radius: 4px;
            font-size: 14px;
            color: #065f46;
        }
        
        .crypto-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 16px;
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .crypto-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 1px solid #e5e7eb;
        }
        
        .crypto-card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }
        
        .crypto-header {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .crypto-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 14px;
        }
        
        .crypto-info h3 {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 2px;
        }
        
        .crypto-info .symbol {
            font-size: 14px;
            color: #6b7280;
            text-transform: uppercase;
        }
        
        .crypto-price {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
            color: #1f2937;
        }
        
        .crypto-change {
            font-size: 16px;
            font-weight: 600;
            padding: 4px 8px;
            border-radius: 6px;
            display: inline-block;
        }
        
        .positive {
            background: #dcfce7;
            color: #166534;
        }
        
        .negative {
            background: #fee2e2;
            color: #dc2626;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #6b7280;
        }
        
        .error {
            background: #fee2e2;
            border-left: 4px solid #dc2626;
            color: #991b1b;
        }
        
        .update-indicator {
            position: fixed;
            top: 80px;
            right: 20px;
            background: #0052ff;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            opacity: 0;
            transition: opacity 0.3s;
            z-index: 101;
        }
        
        .update-indicator.show {
            opacity: 1;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .updating {
            animation: pulse 0.5s ease-in-out;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Real-time Crypto Prices</h1>
    </div>
    
    <div class="status" id="status">
        📡 Connecting to real-time data feed...
    </div>
    
    <div class="update-indicator" id="updateIndicator">
        📊 Updating prices...
    </div>
    
    <div class="crypto-grid" id="cryptoGrid">
        <div class="loading">Loading real-time cryptocurrency prices...</div>
    </div>

    <script>
        class RealTimeCryptoTracker {
            constructor() {
                this.coins = {
                    'bitcoin': { name: 'Bitcoin', symbol: 'BTC', color: '#f7931a' },
                    'ethereum': { name: 'Ethereum', symbol: 'ETH', color: '#627eea' },
                    'binancecoin': { name: 'BNB', symbol: 'BNB', color: '#f3ba2f' },
                    'cardano': { name: 'Cardano', symbol: 'ADA', color: '#0033ad' },
                    'solana': { name: 'Solana', symbol: 'SOL', color: '#9945ff' },
                    'polkadot': { name: 'Polkadot', symbol: 'DOT', color: '#e6007a' },
                    'dogecoin': { name: 'Dogecoin', symbol: 'DOGE', color: '#c2a633' },
                    'avalanche-2': { name: 'Avalanche', symbol: 'AVAX', color: '#e84142' },
                    'polygon': { name: 'Polygon', symbol: 'MATIC', color: '#8247e5' },
                    'chainlink': { name: 'Chainlink', symbol: 'LINK', color: '#375bd2' },
                    'litecoin': { name: 'Litecoin', symbol: 'LTC', color: '#bfbbbb' },
                    'bitcoin-cash': { name: 'Bitcoin Cash', symbol: 'BCH', color: '#8dc351' },
                    'stellar': { name: 'Stellar', symbol: 'XLM', color: '#7d00ff' },
                    'vechain': { name: 'VeChain', symbol: 'VET', color: '#15bdff' },
                    'filecoin': { name: 'Filecoin', symbol: 'FIL', color: '#0090ff' }
                };
                
                this.grid = document.getElementById('cryptoGrid');
                this.status = document.getElementById('status');
                this.updateIndicator = document.getElementById('updateIndicator');
                this.lastPrices = {};
                
                this.init();
            }
            
            async init() {
                console.log('🚀 Starting Real-time Crypto Tracker...');
                await this.fetchPrices();
                this.startRealTimeUpdates();
            }
            
            async fetchPrices() {
                const apiSources = [
                    {
                        name: 'CoinGecko',
                        url: `https://api.coingecko.com/api/v3/simple/price?ids=${Object.keys(this.coins).join(',')}&vs_currencies=usd&include_24hr_change=true`,
                        parser: (data) => data
                    },
                    {
                        name: 'CoinCap',
                        url: 'https://api.coincap.io/v2/assets?limit=50',
                        parser: (data) => {
                            const result = {};
                            data.data.forEach(coin => {
                                const coinId = this.symbolToCoinId(coin.symbol);
                                if (coinId) {
                                    result[coinId] = {
                                        usd: parseFloat(coin.priceUsd),
                                        usd_24h_change: parseFloat(coin.changePercent24Hr)
                                    };
                                }
                            });
                            return result;
                        }
                    }
                ];
                
                for (const source of apiSources) {
                    try {
                        console.log(`🔄 Trying ${source.name}...`);
                        this.showUpdateIndicator();
                        
                        const response = await fetch(source.url);
                        const data = await response.json();
                        const parsedData = source.parser(data);
                        
                        if (Object.keys(parsedData).length > 0) {
                            this.updateDisplay(parsedData);
                            this.updateStatus(`✅ Connected to ${source.name} - Live data updating every second`);
                            console.log(`✅ ${source.name} connected successfully`);
                            return true;
                        }
                    } catch (error) {
                        console.warn(`❌ ${source.name} failed:`, error.message);
                        continue;
                    }
                }
                
                this.updateStatus('❌ Unable to connect to any data source. Please check your internet connection.', true);
                return false;
            }
            
            symbolToCoinId(symbol) {
                const mapping = {
                    'BTC': 'bitcoin',
                    'ETH': 'ethereum',
                    'BNB': 'binancecoin',
                    'ADA': 'cardano',
                    'SOL': 'solana',
                    'DOT': 'polkadot',
                    'DOGE': 'dogecoin',
                    'AVAX': 'avalanche-2',
                    'MATIC': 'polygon',
                    'LINK': 'chainlink',
                    'LTC': 'litecoin',
                    'BCH': 'bitcoin-cash',
                    'XLM': 'stellar',
                    'VET': 'vechain',
                    'FIL': 'filecoin'
                };
                return mapping[symbol];
            }
            
            updateDisplay(priceData) {
                let html = '';
                
                Object.entries(this.coins).forEach(([coinId, coinInfo]) => {
                    const data = priceData[coinId];
                    if (!data) return;
                    
                    const price = data.usd;
                    const change24h = data.usd_24h_change || 0;
                    const isPositive = change24h >= 0;
                    
                    const formattedPrice = price >= 1 
                        ? `$${price.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}`
                        : `$${price.toFixed(6)}`;
                    
                    const changeClass = isPositive ? 'positive' : 'negative';
                    const changeSymbol = isPositive ? '+' : '';
                    
                    // Check if price changed for animation
                    const priceChanged = this.lastPrices[coinId] && this.lastPrices[coinId] !== price;
                    const cardClass = priceChanged ? 'crypto-card updating' : 'crypto-card';
                    
                    html += `
                        <div class="${cardClass}" data-coin="${coinId}">
                            <div class="crypto-header">
                                <div class="crypto-icon" style="background: ${coinInfo.color}">
                                    ${coinInfo.symbol.substring(0, 3)}
                                </div>
                                <div class="crypto-info">
                                    <h3>${coinInfo.name}</h3>
                                    <div class="symbol">${coinInfo.symbol}</div>
                                </div>
                            </div>
                            <div class="crypto-price">${formattedPrice}</div>
                            <div class="crypto-change ${changeClass}">
                                ${changeSymbol}${change24h.toFixed(2)}% (24h)
                            </div>
                        </div>
                    `;
                    
                    this.lastPrices[coinId] = price;
                });
                
                this.grid.innerHTML = html;
                this.hideUpdateIndicator();
            }
            
            updateStatus(message, isError = false) {
                this.status.textContent = message;
                this.status.className = isError ? 'status error' : 'status';
            }
            
            showUpdateIndicator() {
                this.updateIndicator.classList.add('show');
            }
            
            hideUpdateIndicator() {
                setTimeout(() => {
                    this.updateIndicator.classList.remove('show');
                }, 1000);
            }
            
            startRealTimeUpdates() {
                // Update every 1 second for real-time feel
                setInterval(async () => {
                    await this.fetchPrices();
                }, 1000);
            }
        }
        
        // Start the tracker when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new RealTimeCryptoTracker();
        });
    </script>
</body>
</html>
