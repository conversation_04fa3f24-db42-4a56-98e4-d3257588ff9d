<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Coinbase价格连接测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        
        .container {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .status {
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 600;
        }
        
        .connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .price-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-top: 20px;
        }
        
        .price-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 16px;
            border: 1px solid #e9ecef;
        }
        
        .coin-name {
            font-weight: 600;
            font-size: 16px;
            margin-bottom: 8px;
        }
        
        .price {
            font-size: 18px;
            font-weight: 700;
            color: #1a1a1a;
        }
        
        .change {
            font-size: 14px;
            margin-top: 4px;
        }
        
        .positive {
            color: #00d4aa;
        }
        
        .negative {
            color: #ff6b6b;
        }
        
        .log {
            background: #f1f3f4;
            border-radius: 8px;
            padding: 16px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        
        .log-entry {
            margin-bottom: 4px;
            padding: 2px 0;
        }
        
        .log-info {
            color: #1976d2;
        }
        
        .log-error {
            color: #d32f2f;
        }
        
        .log-success {
            color: #388e3c;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Coinbase实时价格测试</h1>
        
        <div id="status" class="status disconnected">
            连接中...
        </div>
        
        <div class="price-grid" id="priceGrid">
            <!-- 价格卡片将在这里动态生成 -->
        </div>
        
        <div class="log" id="log">
            <div class="log-entry log-info">初始化WebSocket连接...</div>
        </div>
    </div>

    <script>
        class PriceTestClient {
            constructor() {
                this.ws = null;
                this.isConnected = false;
                this.priceData = {};
                this.logElement = document.getElementById('log');
                this.statusElement = document.getElementById('status');
                this.priceGridElement = document.getElementById('priceGrid');
                
                this.init();
            }
            
            init() {
                this.log('开始连接到价格服务...', 'info');
                this.connect();
            }
            
            connect() {
                try {
                    this.ws = new WebSocket('ws://localhost:8765');
                    
                    this.ws.onopen = (event) => {
                        this.isConnected = true;
                        this.log('✅ WebSocket连接成功', 'success');
                        this.updateStatus(true);
                        
                        // 订阅所有币种
                        const symbols = ['BTC-USD', 'ETH-USD', 'ADA-USD', 'SOL-USD', 'XRP-USD'];
                        this.subscribe(symbols);
                    };
                    
                    this.ws.onmessage = (event) => {
                        try {
                            const message = JSON.parse(event.data);
                            this.handleMessage(message);
                        } catch (error) {
                            this.log(`❌ 解析消息失败: ${error.message}`, 'error');
                        }
                    };
                    
                    this.ws.onclose = (event) => {
                        this.isConnected = false;
                        this.log('🔌 WebSocket连接已关闭', 'info');
                        this.updateStatus(false);
                        
                        // 5秒后重连
                        setTimeout(() => {
                            this.log('🔄 尝试重新连接...', 'info');
                            this.connect();
                        }, 5000);
                    };
                    
                    this.ws.onerror = (error) => {
                        this.log(`❌ WebSocket错误: ${error}`, 'error');
                    };
                    
                } catch (error) {
                    this.log(`❌ 连接失败: ${error.message}`, 'error');
                }
            }
            
            subscribe(symbols) {
                if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                    const message = {
                        type: 'subscribe',
                        symbols: symbols
                    };
                    this.ws.send(JSON.stringify(message));
                    this.log(`📡 订阅币种: ${symbols.join(', ')}`, 'info');
                }
            }
            
            handleMessage(message) {
                switch (message.type) {
                    case 'initial_data':
                        this.log('📊 收到初始价格数据', 'success');
                        this.priceData = message.data;
                        this.updatePriceDisplay();
                        break;
                        
                    case 'subscribed_data':
                        this.log('📋 收到订阅数据', 'success');
                        Object.assign(this.priceData, message.data);
                        this.updatePriceDisplay();
                        break;
                        
                    case 'price_update':
                        this.log('💰 价格更新', 'info');
                        Object.assign(this.priceData, message.data);
                        this.updatePriceDisplay();
                        break;
                        
                    default:
                        this.log(`❓ 未知消息类型: ${message.type}`, 'info');
                }
            }
            
            updatePriceDisplay() {
                this.priceGridElement.innerHTML = '';
                
                Object.keys(this.priceData).forEach(symbol => {
                    const data = this.priceData[symbol];
                    const card = this.createPriceCard(symbol, data);
                    this.priceGridElement.appendChild(card);
                });
            }
            
            createPriceCard(symbol, data) {
                const card = document.createElement('div');
                card.className = 'price-card';
                
                const coinName = document.createElement('div');
                coinName.className = 'coin-name';
                coinName.textContent = symbol.replace('-USD', '');
                
                const price = document.createElement('div');
                price.className = 'price';
                price.textContent = this.formatPrice(data.price);
                
                const change = document.createElement('div');
                change.className = `change ${data.change_percent_24h >= 0 ? 'positive' : 'negative'}`;
                change.textContent = this.formatChange(data.change_percent_24h);
                
                const lastUpdated = document.createElement('div');
                lastUpdated.style.fontSize = '10px';
                lastUpdated.style.color = '#666';
                lastUpdated.style.marginTop = '8px';
                lastUpdated.textContent = `更新: ${new Date(data.last_updated).toLocaleTimeString()}`;
                
                card.appendChild(coinName);
                card.appendChild(price);
                card.appendChild(change);
                card.appendChild(lastUpdated);
                
                return card;
            }
            
            formatPrice(price) {
                if (price >= 1) {
                    return `$${price.toFixed(2)}`;
                } else if (price >= 0.01) {
                    return `$${price.toFixed(4)}`;
                } else {
                    return `$${price.toFixed(6)}`;
                }
            }
            
            formatChange(change) {
                const sign = change >= 0 ? '+' : '';
                return `${sign}${change.toFixed(2)}%`;
            }
            
            updateStatus(connected) {
                if (connected) {
                    this.statusElement.textContent = '✅ 已连接到价格服务';
                    this.statusElement.className = 'status connected';
                } else {
                    this.statusElement.textContent = '❌ 连接断开';
                    this.statusElement.className = 'status disconnected';
                }
            }
            
            log(message, type = 'info') {
                const entry = document.createElement('div');
                entry.className = `log-entry log-${type}`;
                entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
                
                this.logElement.appendChild(entry);
                this.logElement.scrollTop = this.logElement.scrollHeight;
                
                // 限制日志条目数量
                const entries = this.logElement.children;
                if (entries.length > 50) {
                    this.logElement.removeChild(entries[0]);
                }
            }
        }
        
        // 启动测试客户端
        const testClient = new PriceTestClient();
    </script>
</body>
</html>
