<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时价格更新演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 3rem;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
            margin: 10px 0;
        }
        
        .crypto-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .crypto-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        
        .crypto-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.15);
        }
        
        .crypto-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .crypto-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 18px;
            margin-right: 15px;
        }
        
        .crypto-info h3 {
            margin: 0;
            font-size: 1.5rem;
        }
        
        .crypto-info p {
            margin: 5px 0 0 0;
            opacity: 0.8;
        }
        
        .crypto-price {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 15px 0;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }
        
        .crypto-change {
            font-size: 1.2rem;
            font-weight: 600;
            padding: 8px 16px;
            border-radius: 25px;
            display: inline-block;
        }
        
        .crypto-change.positive {
            background: rgba(0, 212, 170, 0.3);
            color: #00d4aa;
        }
        
        .crypto-change.negative {
            background: rgba(255, 107, 107, 0.3);
            color: #ff6b6b;
        }
        
        .last-updated {
            margin-top: 15px;
            font-size: 0.9rem;
            opacity: 0.7;
        }
        
        .status-bar {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 15px 25px;
            text-align: center;
            margin-bottom: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .update-animation {
            animation: priceUpdate 1s ease-out;
        }
        
        @keyframes priceUpdate {
            0% { 
                background: rgba(0, 212, 170, 0.5);
                transform: scale(1.02);
            }
            100% { 
                background: rgba(255, 255, 255, 0.1);
                transform: scale(1);
            }
        }
        
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        
        .btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 实时加密货币价格</h1>
            <p>使用CoinGecko免费API，每30秒自动更新</p>
        </div>
        
        <div class="status-bar" id="statusBar">
            正在加载价格数据...
        </div>
        
        <div class="controls">
            <button class="btn" onclick="cryptoUpdater.fetchPrices()">立即刷新</button>
            <button class="btn" onclick="cryptoUpdater.toggleAutoUpdate()" id="toggleBtn">暂停更新</button>
        </div>
        
        <div class="crypto-grid" id="cryptoGrid">
            <!-- 价格卡片将在这里生成 -->
        </div>
    </div>

    <script>
        class CryptoUpdater {
            constructor() {
                this.updateInterval = 30000; // 30秒
                this.priceData = {};
                this.lastUpdateTime = 0;
                this.isAutoUpdateEnabled = true;
                this.updateTimer = null;
                
                this.cryptos = [
                    { id: 'bitcoin', symbol: 'BTC', name: 'Bitcoin', color: '#f7931a' },
                    { id: 'ethereum', symbol: 'ETH', name: 'Ethereum', color: '#627eea' },
                    { id: 'binancecoin', symbol: 'BNB', name: 'BNB', color: '#f3ba2f' },
                    { id: 'cardano', symbol: 'ADA', name: 'Cardano', color: '#0033ad' },
                    { id: 'solana', symbol: 'SOL', name: 'Solana', color: '#9945ff' },
                    { id: 'polkadot', symbol: 'DOT', name: 'Polkadot', color: '#e6007a' }
                ];
                
                this.init();
            }
            
            async init() {
                console.log('🚀 启动实时价格更新系统...');
                this.createCryptoCards();
                await this.fetchPrices();
                this.startAutoUpdate();
            }
            
            createCryptoCards() {
                const grid = document.getElementById('cryptoGrid');
                grid.innerHTML = '';
                
                this.cryptos.forEach(crypto => {
                    const card = document.createElement('div');
                    card.className = 'crypto-card';
                    card.id = `card-${crypto.symbol}`;
                    
                    card.innerHTML = `
                        <div class="crypto-header">
                            <div class="crypto-icon" style="background: linear-gradient(45deg, ${crypto.color}, ${crypto.color}80)">
                                ${crypto.symbol.charAt(0)}
                            </div>
                            <div class="crypto-info">
                                <h3>${crypto.symbol}</h3>
                                <p>${crypto.name}</p>
                            </div>
                        </div>
                        <div class="crypto-price" id="price-${crypto.symbol}">$0.00</div>
                        <div class="crypto-change positive" id="change-${crypto.symbol}">+0.00%</div>
                        <div class="last-updated" id="updated-${crypto.symbol}">从未更新</div>
                    `;
                    
                    grid.appendChild(card);
                });
            }
            
            async fetchPrices() {
                try {
                    const now = Date.now();
                    if (now - this.lastUpdateTime < 25000) {
                        console.log('⏰ 请求过于频繁，跳过此次更新');
                        return false;
                    }
                    
                    this.updateStatus('正在获取最新价格...', '#feca57');
                    console.log('🔄 获取最新价格数据...');
                    
                    const coinIds = this.cryptos.map(c => c.id).join(',');
                    const url = `https://api.coingecko.com/api/v3/simple/price?ids=${coinIds}&vs_currencies=usd&include_24hr_change=true&include_last_updated_at=true`;
                    
                    const response = await fetch(url);
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    
                    const data = await response.json();
                    this.priceData = data;
                    this.lastUpdateTime = now;
                    
                    this.updateDisplay();
                    this.updateStatus(`✅ 成功更新 ${Object.keys(data).length} 个币种价格 - ${new Date().toLocaleTimeString()}`, '#00d4aa');
                    
                    console.log(`✅ 成功更新价格数据`);
                    return true;
                    
                } catch (error) {
                    console.error('❌ 获取价格数据失败:', error);
                    this.updateStatus(`❌ 获取价格失败: ${error.message}`, '#ff6b6b');
                    return false;
                }
            }
            
            updateDisplay() {
                this.cryptos.forEach(crypto => {
                    const data = this.priceData[crypto.id];
                    if (!data) return;
                    
                    const priceElement = document.getElementById(`price-${crypto.symbol}`);
                    const changeElement = document.getElementById(`change-${crypto.symbol}`);
                    const updatedElement = document.getElementById(`updated-${crypto.symbol}`);
                    const cardElement = document.getElementById(`card-${crypto.symbol}`);
                    
                    // 更新价格
                    const price = data.usd || 0;
                    priceElement.textContent = this.formatPrice(price);
                    
                    // 更新涨跌幅
                    const change = data.usd_24h_change || 0;
                    changeElement.textContent = this.formatChange(change);
                    changeElement.className = `crypto-change ${change >= 0 ? 'positive' : 'negative'}`;
                    
                    // 更新时间
                    const lastUpdated = data.last_updated_at ? new Date(data.last_updated_at * 1000) : new Date();
                    updatedElement.textContent = `更新于 ${lastUpdated.toLocaleTimeString()}`;
                    
                    // 添加更新动画
                    cardElement.classList.add('update-animation');
                    setTimeout(() => {
                        cardElement.classList.remove('update-animation');
                    }, 1000);
                });
            }
            
            formatPrice(price) {
                if (price >= 1) {
                    return `$${price.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}`;
                } else if (price >= 0.01) {
                    return `$${price.toFixed(4)}`;
                } else {
                    return `$${price.toFixed(6)}`;
                }
            }
            
            formatChange(change) {
                const sign = change >= 0 ? '+' : '';
                return `${sign}${change.toFixed(2)}%`;
            }
            
            updateStatus(message, color = '#ffffff') {
                const statusBar = document.getElementById('statusBar');
                statusBar.textContent = message;
                statusBar.style.color = color;
            }
            
            startAutoUpdate() {
                if (this.updateTimer) {
                    clearInterval(this.updateTimer);
                }
                
                this.updateTimer = setInterval(() => {
                    if (this.isAutoUpdateEnabled) {
                        this.fetchPrices();
                    }
                }, this.updateInterval);
                
                console.log('⏰ 自动更新已启动，每30秒更新一次');
            }
            
            toggleAutoUpdate() {
                const toggleBtn = document.getElementById('toggleBtn');
                
                if (this.isAutoUpdateEnabled) {
                    this.isAutoUpdateEnabled = false;
                    toggleBtn.textContent = '启动更新';
                    this.updateStatus('⏸️ 自动更新已暂停', '#feca57');
                } else {
                    this.isAutoUpdateEnabled = true;
                    toggleBtn.textContent = '暂停更新';
                    this.updateStatus('▶️ 自动更新已启动', '#00d4aa');
                }
            }
        }
        
        // 启动系统
        const cryptoUpdater = new CryptoUpdater();
    </script>
</body>
</html>
