<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>身份验证信息收集</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.4;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 12px;
            background-color: #f5f5f5;
            font-size: 14px;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin: 10px 0 15px;
            font-size: 18px;
        }
        .form-section {
            background-color: white;
            padding: 12px 15px;
            border-radius: 4px;
            margin-bottom: 12px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.05);
        }
        h2 {
            color: #3498db;
            font-size: 15px;
            margin: 0 0 10px;
            padding-bottom: 3px;
            border-bottom: 1px solid #e0e0e0;
        }
        .form-group {
            margin-bottom: 12px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            font-size: 13px;
        }
        .required:after {
            content: " *";
            color: red;
        }
        input[type="text"],
        input[type="date"],
        input[type="email"],
        input[type="tel"],
        select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
            font-size: 13px;
        }
        .name-fields {
            display: flex;
            gap: 8px;
        }
        .name-fields div {
            flex: 1;
        }
        .radio-group {
            margin-top: 8px;
        }
        .radio-option {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
        }
        .radio-option input {
            margin-right: 6px;
        }
        .upload-area {
            border: 1px dashed #ccc;
            padding: 15px;
            text-align: center;
            border-radius: 4px;
            margin-top: 8px;
            background-color: #f9f9f9;
        }
        .upload-icon {
            font-size: 24px;
            color: #3498db;
            margin-bottom: 5px;
        }
        .button-group {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 20px 0 10px;
        }
        .btn {
            padding: 10px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            font-size: 14px;
            transition: background-color 0.2s;
            flex: 1;
            max-width: 160px;
        }
        .btn-primary {
            background-color: #3498db;
            color: white;
        }
        .btn-secondary {
            background-color: #95a5a6;
            color: white;
        }
        .note {
            font-size: 12px;
            color: #777;
            margin-top: 5px;
        }
        .document-options {
            margin: 8px 0;
        }
    </style>
</head>
<body>
    <h1>身份验证信息收集</h1>
    
    <form id="verificationForm">
        <div class="form-section">
            <h2>1. 个人信息</h2>
            
            <div class="form-group">
                <label class="required">法定全名</label>
                <div class="name-fields">
                    <div>
                        <label>姓氏</label>
                        <input type="text" name="last_name" required>
                    </div>
                    <div>
                        <label>名字</label>
                        <input type="text" name="first_name" required>
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label class="required">出生日期</label>
                <input type="date" name="dob" required>
            </div>
        </div>
        
        <div class="form-section">
            <h2>2. 身份证明文件</h2>
            
            <div class="form-group document-options">
                <label class="required">证件类型</label>
                <div class="radio-group">
                    <div class="radio-option">
                        <input type="radio" id="passport" name="document_type" value="passport" required>
                        <label for="passport">美国护照</label>
                    </div>
                    <div class="radio-option">
                        <input type="radio" id="driver_license" name="document_type" value="driver_license">
                        <label for="driver_license">美国驾照</label>
                    </div>
                    <div class="radio-option">
                        <input type="radio" id="state_id" name="document_type" value="state_id">
                        <label for="state_id">州身份证</label>
                    </div>
                    <div class="radio-option">
                        <input type="radio" id="green_card" name="document_type" value="green_card">
                        <label for="green_card">永久居民卡(绿卡)</label>
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label class="required">证件正面照片</label>
                <div class="upload-area">
                    <div class="upload-icon">↑</div>
                    <p>点击或拖放文件到此处上传</p>
                    <input type="file" name="document_front" accept="image/*,.pdf" required>
                </div>
                <p class="note">支持JPG, PNG或PDF格式，文件大小不超过5MB</p>
            </div>
            
            <div class="form-group">
                <label>证件背面照片(如有)</label>
                <div class="upload-area">
                    <div class="upload-icon">↑</div>
                    <p>点击或拖放文件到此处上传</p>
                    <input type="file" name="document_back" accept="image/*,.pdf">
                </div>
            </div>
        </div>
        
        <div class="form-section">
            <h2>3. 地址信息</h2>
            
            <div class="form-group">
                <label class="required">街道地址</label>
                <input type="text" name="street_address" required>
            </div>
            
            <div class="form-group">
                <label>公寓/单元号(如有)</label>
                <input type="text" name="apt_unit">
            </div>
            
            <div class="form-group">
                <div class="name-fields">
                    <div>
                        <label class="required">城市</label>
                        <input type="text" name="city" required>
                    </div>
                    <div>
                        <label class="required">州</label>
                        <input type="text" name="state" required>
                    </div>
                    <div>
                        <label class="required">邮编</label>
                        <input type="text" name="zip_code" required>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="form-section">
            <h2>4. 联系方式</h2>
            
            <div class="form-group">
                <label class="required">电话号码</label>
                <input type="tel" name="phone" placeholder="(*************" required>
            </div>
            
            <div class="form-group">
                <label class="required">电子邮箱</label>
                <input type="email" name="email" placeholder="<EMAIL>" required>
            </div>
        </div>
        
        <div class="button-group">
            <button type="button" class="btn btn-secondary" id="backBtn">返回</button>
            <button type="submit" class="btn btn-primary">提交验证</button>
        </div>
    </form>

    <script>
        // 返回按钮功能
        document.getElementById('backBtn').addEventListener('click', function() {
            window.history.back();
        });
        
        // 表单提交
        document.getElementById('verificationForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // 验证证件类型是否选择
            const documentType = document.querySelector('input[name="document_type"]:checked');
            if(!documentType) {
                alert('请选择证件类型');
                return;
            }
            
            // 验证证件正面照片是否上传
            const frontFile = document.querySelector('input[name="document_front"]').files[0];
            if(!frontFile) {
                alert('请上传证件正面照片');
                return;
            }
            
            // 这里应该是表单提交代码
            alert('验证信息已提交，请等待审核结果');
            console.log('表单数据:', new FormData(this));
            
            // 实际应用中这里应该是表单提交代码
            // this.submit();
        });
    </script>
</body>
</html>