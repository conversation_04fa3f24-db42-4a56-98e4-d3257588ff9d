<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>身份验证所需信息</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.5;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
        }
        h1 {
            color: #000;
            text-align: center;
            font-size: 22px;
            margin-bottom: 10px;
            font-weight: normal;
        }
        .info-list {
            margin: 30px 0;
        }
        .info-item {
            padding: 15px 0;
            border-bottom: 1px solid #eee;
            font-size: 16px;
        }
        .buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 30px;
        }
        .btn {
            padding: 10px 20px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            text-align: center;
            text-decoration: none;
            min-width: 120px;
        }
        .btn-primary {
            background-color: #1a73e8;
            color: white;
            border: none;
        }
        .btn-secondary {
            background-color: #fff;
            color: #1a73e8;
            border: 1px solid #dadce0;
        }
    </style>
</head>
<body>
    <h1>身份验证所需信息</h1>
    
    <div class="info-list">
        <div class="info-item">个人信息</div>
        <div class="info-item">身份证明文件</div>
        <div class="info-item">地址信息</div>
        <div class="info-item">联系方式</div>
    </div>
    
    <div class="buttons">
        <a href="#" class="btn btn-secondary">返回</a>
        <a href="verification-form.html" class="btn btn-primary">开始验证</a>
    </div>

    <script>
        document.querySelector('.btn-secondary').addEventListener('click', function(e) {
            e.preventDefault();
            window.history.back();
        });
    </script>
</body>
</html>