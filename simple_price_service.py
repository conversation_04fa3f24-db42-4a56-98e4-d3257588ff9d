#!/usr/bin/env python3.10
"""
简化版Coinbase价格服务
使用公共API端点，无需API密钥
"""

import asyncio
import json
import logging
import time
import aiohttp
import websockets
from websockets.server import serve
from datetime import datetime
from typing import Dict, List

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SimplePriceService:
    """简化版价格服务"""
    
    def __init__(self):
        self.price_data = {}
        self.connected_clients = set()
        
        # 支持的交易对
        self.supported_pairs = [
            'BTC-USD', 'ETH-USD', 'ADA-USD', 'SOL-USD', 'XRP-USD',
            'DOGE-USD', 'MATIC-USD', 'LTC-USD', 'LINK-USD', 'DOT-USD',
            'UNI-USD', 'AVAX-USD', 'ATOM-USD', 'ALGO-USD', 'VET-USD'
        ]
        
        # Coinbase公共API基础URL
        self.base_url = "https://api.exchange.coinbase.com"
        
    async def fetch_price_data(self):
        """获取价格数据"""
        try:
            async with aiohttp.ClientSession() as session:
                for pair in self.supported_pairs:
                    try:
                        # 获取ticker数据
                        ticker_url = f"{self.base_url}/products/{pair}/ticker"
                        async with session.get(ticker_url) as response:
                            if response.status == 200:
                                ticker_data = await response.json()
                                
                                # 获取24小时统计
                                stats_url = f"{self.base_url}/products/{pair}/stats"
                                async with session.get(stats_url) as stats_response:
                                    if stats_response.status == 200:
                                        stats_data = await stats_response.json()
                                        
                                        current_price = float(ticker_data.get('price', 0))
                                        open_price = float(stats_data.get('open', 0))
                                        
                                        # 计算24小时变化
                                        change_24h = current_price - open_price if open_price > 0 else 0
                                        change_percent_24h = (change_24h / open_price * 100) if open_price > 0 else 0
                                        
                                        self.price_data[pair] = {
                                            'symbol': pair,
                                            'price': current_price,
                                            'change_24h': change_24h,
                                            'change_percent_24h': change_percent_24h,
                                            'volume_24h': float(stats_data.get('volume', 0)),
                                            'high_24h': float(stats_data.get('high', 0)),
                                            'low_24h': float(stats_data.get('low', 0)),
                                            'last_updated': datetime.now().isoformat()
                                        }
                                        
                                        logger.info(f"更新 {pair}: ${current_price:.2f} ({change_percent_24h:+.2f}%)")
                                        
                    except Exception as e:
                        logger.error(f"获取 {pair} 数据失败: {e}")
                        continue
                        
        except Exception as e:
            logger.error(f"获取价格数据失败: {e}")
    
    async def price_update_loop(self):
        """价格更新循环"""
        while True:
            try:
                await self.fetch_price_data()
                
                # 广播更新给所有客户端
                if self.connected_clients and self.price_data:
                    message = json.dumps({
                        'type': 'price_update',
                        'data': self.price_data
                    })
                    
                    disconnected_clients = set()
                    for client in self.connected_clients:
                        try:
                            await client.send(message)
                        except websockets.exceptions.ConnectionClosed:
                            disconnected_clients.add(client)
                        except Exception as e:
                            logger.error(f"发送消息失败: {e}")
                            disconnected_clients.add(client)
                    
                    # 移除断开的客户端
                    self.connected_clients -= disconnected_clients
                
                # 等待30秒后再次更新
                await asyncio.sleep(30)
                
            except Exception as e:
                logger.error(f"价格更新循环错误: {e}")
                await asyncio.sleep(10)
    
    async def handle_client(self, websocket, path):
        """处理客户端连接"""
        logger.info(f"新客户端连接: {websocket.remote_address}")
        self.connected_clients.add(websocket)
        
        try:
            # 发送初始数据
            if self.price_data:
                await websocket.send(json.dumps({
                    'type': 'initial_data',
                    'data': self.price_data
                }))
            
            # 处理客户端消息
            async for message in websocket:
                try:
                    data = json.loads(message)
                    
                    if data.get('type') == 'subscribe':
                        symbols = data.get('symbols', [])
                        logger.info(f"客户端订阅: {symbols}")
                        
                        # 发送订阅的数据
                        filtered_data = {
                            symbol: self.price_data[symbol]
                            for symbol in symbols
                            if symbol in self.price_data
                        }
                        
                        if filtered_data:
                            await websocket.send(json.dumps({
                                'type': 'subscribed_data',
                                'data': filtered_data
                            }))
                    
                    elif data.get('type') == 'ping':
                        # 心跳响应
                        await websocket.send(json.dumps({'type': 'pong'}))
                        
                except json.JSONDecodeError:
                    logger.error("收到无效JSON消息")
                except Exception as e:
                    logger.error(f"处理消息失败: {e}")
                    
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"客户端断开连接: {websocket.remote_address}")
        except Exception as e:
            logger.error(f"客户端连接错误: {e}")
        finally:
            self.connected_clients.discard(websocket)
    
    async def start_server(self, host='localhost', port=8765):
        """启动服务器"""
        logger.info(f"启动价格服务器 ws://{host}:{port}")
        
        # 启动价格更新循环
        asyncio.create_task(self.price_update_loop())
        
        # 启动WebSocket服务器
        async with serve(self.handle_client, host, port):
            logger.info("服务器已启动，等待客户端连接...")
            await asyncio.Future()  # 永远运行

async def main():
    """主函数"""
    service = SimplePriceService()
    
    # 首次获取数据
    logger.info("获取初始价格数据...")
    await service.fetch_price_data()
    
    # 启动服务器
    await service.start_server()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("服务已停止")
    except Exception as e:
        logger.error(f"服务启动失败: {e}")
