<!-- Modal Popups for Transfer and Buy&Sell -->
<div id="transferModal" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background-color:rgba(0,0,0,0.7); z-index:1000;">
  <div style="position:relative; width:90%; max-width:600px; margin:50px auto; background-color:white; border-radius:8px; padding:20px; box-shadow:0 4px 8px rgba(0,0,0,0.2);">
    <button id="closeTransferModal" style="position:absolute; right:10px; top:10px; background:none; border:none; font-size:24px; cursor:pointer;">&times;</button>
    <h2 style="margin-top:0; color:#0052FF;">Transfer</h2>
    <div style="display:flex; margin-bottom:20px;">
      <img src="transfer.jpg" alt="Transfer Options" style="width:100%; border-radius:4px;">
    </div>
    <div style="margin-top:20px;">
      <button style="background-color:#0052FF; color:white; border:none; padding:12px 20px; border-radius:4px; cursor:pointer; font-weight:bold; width:100%;">Continue</button>
    </div>
  </div>
</div>

<div id="buySellModal" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background-color:rgba(0,0,0,0.7); z-index:1000;">
  <div style="position:relative; width:90%; max-width:600px; margin:50px auto; background-color:white; border-radius:8px; padding:20px; box-shadow:0 4px 8px rgba(0,0,0,0.2);">
    <button id="closeBuySellModal" style="position:absolute; right:10px; top:10px; background:none; border:none; font-size:24px; cursor:pointer;">&times;</button>
    <h2 style="margin-top:0; color:#0052FF;">Buy & Sell</h2>
    <div style="display:flex; margin-bottom:20px;">
      <img src="buy&sell.jpg" alt="Buy & Sell Options" style="width:100%; border-radius:4px;">
    </div>
    <div style="margin-top:20px;">
      <button style="background-color:#0052FF; color:white; border:none; padding:12px 20px; border-radius:4px; cursor:pointer; font-weight:bold; width:100%;">Continue</button>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Find all buttons in the bottom navigation
    setTimeout(function() {
      const allButtons = document.querySelectorAll('button');
      
      allButtons.forEach(button => {
        const buttonText = button.textContent.toLowerCase();
        
        if (buttonText.includes('transfer')) {
          button.addEventListener('click', function(e) {
            e.preventDefault();
            document.getElementById('transferModal').style.display = 'block';
          });
        } 
        else if (buttonText.includes('buy') || buttonText.includes('sell')) {
          button.addEventListener('click', function(e) {
            e.preventDefault();
            document.getElementById('buySellModal').style.display = 'block';
          });
        }
      });
      
      // Close modal functionality
      document.getElementById('closeTransferModal').addEventListener('click', function() {
        document.getElementById('transferModal').style.display = 'none';
      });
      
      document.getElementById('closeBuySellModal').addEventListener('click', function() {
        document.getElementById('buySellModal').style.display = 'none';
      });
      
      // Close modals when clicking outside the content
      window.addEventListener('click', function(event) {
        const transferModal = document.getElementById('transferModal');
        const buySellModal = document.getElementById('buySellModal');
        
        if (event.target === transferModal) {
          transferModal.style.display = 'none';
        }
        
        if (event.target === buySellModal) {
          buySellModal.style.display = 'none';
        }
      });
    }, 1000); // Small delay to ensure DOM is fully loaded
  });
</script>
