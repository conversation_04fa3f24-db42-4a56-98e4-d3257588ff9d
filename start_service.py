#!/usr/bin/env python3
"""
Coinbase价格服务启动脚本
自动检测依赖并启动合适的服务
"""

import sys
import subprocess
import importlib
import os

def check_package(package_name):
    """检查包是否已安装"""
    try:
        importlib.import_module(package_name)
        return True
    except ImportError:
        return False

def install_package(package_name):
    """安装包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    print("🚀 Coinbase实时价格服务启动器")
    print("=" * 50)
    
    # 检查基础依赖
    required_packages = {
        'aiohttp': 'aiohttp>=3.8.0',
        'websockets': 'websockets>=11.0.0'
    }
    
    missing_packages = []
    
    print("📦 检查依赖包...")
    for package, pip_name in required_packages.items():
        if check_package(package):
            print(f"✅ {package} - 已安装")
        else:
            print(f"❌ {package} - 未安装")
            missing_packages.append(pip_name)
    
    # 安装缺失的包
    if missing_packages:
        print(f"\n📥 安装缺失的依赖包: {', '.join(missing_packages)}")
        for package in missing_packages:
            print(f"正在安装 {package}...")
            if install_package(package):
                print(f"✅ {package} 安装成功")
            else:
                print(f"❌ {package} 安装失败")
                print("请手动运行: pip install", package)
                return False
    
    # 检查Coinbase SDK
    print("\n🔍 检查Coinbase SDK...")
    has_coinbase_sdk = check_package('coinbase')
    
    if has_coinbase_sdk:
        print("✅ Coinbase Advanced SDK - 已安装")
        service_file = "coinbase_price_service.py"
        service_name = "完整版Coinbase服务"
    else:
        print("⚠️  Coinbase Advanced SDK - 未安装，使用简化版服务")
        service_file = "simple_price_service.py"
        service_name = "简化版价格服务"
    
    # 检查服务文件是否存在
    if not os.path.exists(service_file):
        print(f"❌ 服务文件 {service_file} 不存在")
        return False
    
    print(f"\n🎯 启动 {service_name}")
    print("=" * 50)
    print("服务将在 ws://localhost:8765 启动")
    print("请在浏览器中打开 explore.html 查看实时价格")
    print("按 Ctrl+C 停止服务")
    print("=" * 50)
    
    try:
        # 启动服务
        if service_file == "coinbase_price_service.py":
            from coinbase_price_service import CoinbasePriceService
            service = CoinbasePriceService()
            service.run()
        else:
            import asyncio
            from simple_price_service import main as simple_main
            asyncio.run(simple_main())
            
    except KeyboardInterrupt:
        print("\n\n👋 服务已停止")
    except Exception as e:
        print(f"\n❌ 服务启动失败: {e}")
        print("\n🔧 故障排除建议:")
        print("1. 检查端口8765是否被占用")
        print("2. 确认网络连接正常")
        print("3. 查看详细错误信息")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
