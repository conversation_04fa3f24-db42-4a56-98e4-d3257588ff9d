#!/usr/bin/env python3
"""
Coinbase实时价格服务
使用官方Coinbase Advanced API Python SDK获取实时价格数据
"""

import asyncio
import json
import logging
import time
from datetime import datetime
from typing import Dict, List, Optional
import websockets
from websockets.server import serve
from coinbase.rest import RESTClient
from coinbase.websocket import WSClient
import threading
import queue

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CoinbasePriceService:
    """Coinbase价格服务类"""
    
    def __init__(self):
        # 初始化REST客户端（无需API密钥，使用公共端点）
        self.rest_client = RESTClient()
        
        # 价格数据存储
        self.price_data = {}
        self.connected_clients = set()
        
        # 支持的交易对
        self.supported_pairs = [
            'BTC-USD', 'ETH-USD', 'ADA-USD', 'SOL-USD', 'XRP-USD',
            'DOGE-USD', 'MATIC-USD', 'LTC-USD', 'LINK-USD', 'DOT-USD',
            'UNI-USD', 'AVAX-USD', 'ATOM-USD', 'ALGO-USD', 'VET-USD'
        ]
        
        # WebSocket客户端
        self.ws_client = None
        self.price_queue = queue.Queue()
        
    def get_initial_prices(self) -> Dict:
        """获取初始价格数据"""
        try:
            logger.info("获取初始价格数据...")
            
            # 获取所有产品信息
            products = self.rest_client.get_public_products()
            
            for product in products.products:
                if product.product_id in self.supported_pairs:
                    try:
                        # 获取详细产品信息
                        product_detail = self.rest_client.get_public_product(product.product_id)
                        
                        # 获取24小时统计数据
                        candles = self.rest_client.get_public_candles(
                            product_id=product.product_id,
                            start=int(time.time() - 86400),  # 24小时前
                            end=int(time.time()),
                            granularity="ONE_DAY"
                        )
                        
                        # 计算24小时变化
                        price_24h_ago = None
                        current_price = float(product_detail.price) if product_detail.price else 0
                        
                        if candles.candles and len(candles.candles) > 0:
                            price_24h_ago = float(candles.candles[0].open)
                        
                        # 计算变化百分比
                        change_24h = 0
                        change_percent_24h = 0
                        
                        if price_24h_ago and price_24h_ago > 0:
                            change_24h = current_price - price_24h_ago
                            change_percent_24h = (change_24h / price_24h_ago) * 100
                        
                        self.price_data[product.product_id] = {
                            'symbol': product.product_id,
                            'price': current_price,
                            'change_24h': change_24h,
                            'change_percent_24h': change_percent_24h,
                            'volume_24h': float(product_detail.volume_24h) if product_detail.volume_24h else 0,
                            'market_cap': 0,  # 需要额外API获取
                            'last_updated': datetime.now().isoformat(),
                            'status': product_detail.status if hasattr(product_detail, 'status') else 'online'
                        }
                        
                        logger.info(f"获取 {product.product_id} 价格: ${current_price:.2f}")
                        
                    except Exception as e:
                        logger.error(f"获取 {product.product_id} 数据失败: {e}")
                        continue
                        
            logger.info(f"成功获取 {len(self.price_data)} 个币种的价格数据")
            return self.price_data
            
        except Exception as e:
            logger.error(f"获取初始价格数据失败: {e}")
            return {}
    
    def on_websocket_message(self, message):
        """WebSocket消息处理"""
        try:
            data = json.loads(message)
            
            if data.get('channel') == 'ticker':
                events = data.get('events', [])
                
                for event in events:
                    tickers = event.get('tickers', [])
                    
                    for ticker in tickers:
                        product_id = ticker.get('product_id')
                        
                        if product_id in self.supported_pairs:
                            price = float(ticker.get('price', 0))
                            
                            # 更新价格数据
                            if product_id in self.price_data:
                                old_price = self.price_data[product_id]['price']
                                
                                # 计算24小时变化（简化版本）
                                if old_price > 0:
                                    change_24h = price - old_price
                                    change_percent_24h = (change_24h / old_price) * 100
                                else:
                                    change_24h = 0
                                    change_percent_24h = 0
                                
                                self.price_data[product_id].update({
                                    'price': price,
                                    'change_24h': change_24h,
                                    'change_percent_24h': change_percent_24h,
                                    'last_updated': datetime.now().isoformat()
                                })
                                
                                # 将更新放入队列
                                self.price_queue.put({
                                    'type': 'price_update',
                                    'data': {product_id: self.price_data[product_id]}
                                })
                                
                                logger.info(f"更新 {product_id} 价格: ${price:.2f}")
                            
        except Exception as e:
            logger.error(f"处理WebSocket消息失败: {e}")
    
    def start_websocket_feed(self):
        """启动WebSocket价格订阅"""
        try:
            logger.info("启动WebSocket价格订阅...")
            
            self.ws_client = WSClient(on_message=self.on_websocket_message)
            self.ws_client.open()
            
            # 订阅ticker频道
            self.ws_client.ticker(product_ids=self.supported_pairs)
            
            logger.info("WebSocket订阅成功")
            
            # 保持连接
            self.ws_client.run_forever_with_exception_check()
            
        except Exception as e:
            logger.error(f"WebSocket连接失败: {e}")
    
    async def handle_client(self, websocket, path):
        """处理客户端WebSocket连接"""
        logger.info(f"新客户端连接: {websocket.remote_address}")
        self.connected_clients.add(websocket)
        
        try:
            # 发送初始价格数据
            await websocket.send(json.dumps({
                'type': 'initial_data',
                'data': self.price_data
            }))
            
            # 保持连接并处理消息
            async for message in websocket:
                try:
                    data = json.loads(message)
                    
                    if data.get('type') == 'subscribe':
                        # 客户端订阅特定币种
                        symbols = data.get('symbols', [])
                        logger.info(f"客户端订阅: {symbols}")
                        
                        # 发送订阅币种的当前数据
                        filtered_data = {
                            symbol: self.price_data[symbol] 
                            for symbol in symbols 
                            if symbol in self.price_data
                        }
                        
                        await websocket.send(json.dumps({
                            'type': 'subscribed_data',
                            'data': filtered_data
                        }))
                        
                except json.JSONDecodeError:
                    logger.error("收到无效JSON消息")
                except Exception as e:
                    logger.error(f"处理客户端消息失败: {e}")
                    
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"客户端断开连接: {websocket.remote_address}")
        except Exception as e:
            logger.error(f"客户端连接错误: {e}")
        finally:
            self.connected_clients.discard(websocket)
    
    async def broadcast_updates(self):
        """广播价格更新给所有连接的客户端"""
        while True:
            try:
                # 检查是否有价格更新
                if not self.price_queue.empty():
                    update = self.price_queue.get()
                    
                    # 广播给所有连接的客户端
                    if self.connected_clients:
                        message = json.dumps(update)
                        disconnected_clients = set()
                        
                        for client in self.connected_clients:
                            try:
                                await client.send(message)
                            except websockets.exceptions.ConnectionClosed:
                                disconnected_clients.add(client)
                            except Exception as e:
                                logger.error(f"发送消息给客户端失败: {e}")
                                disconnected_clients.add(client)
                        
                        # 移除断开的客户端
                        self.connected_clients -= disconnected_clients
                
                await asyncio.sleep(0.1)  # 100ms检查间隔
                
            except Exception as e:
                logger.error(f"广播更新失败: {e}")
                await asyncio.sleep(1)
    
    async def start_server(self, host='localhost', port=8765):
        """启动WebSocket服务器"""
        logger.info(f"启动WebSocket服务器 ws://{host}:{port}")
        
        # 启动广播任务
        asyncio.create_task(self.broadcast_updates())
        
        # 启动WebSocket服务器
        async with serve(self.handle_client, host, port):
            logger.info("WebSocket服务器已启动")
            await asyncio.Future()  # 永远运行
    
    def run(self):
        """运行服务"""
        logger.info("启动Coinbase价格服务...")
        
        # 获取初始价格数据
        self.get_initial_prices()
        
        # 在后台线程启动WebSocket价格订阅
        ws_thread = threading.Thread(target=self.start_websocket_feed, daemon=True)
        ws_thread.start()
        
        # 启动WebSocket服务器
        asyncio.run(self.start_server())

if __name__ == "__main__":
    service = CoinbasePriceService()
    service.run()
